<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WordLoom - Backend API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
        }
        .endpoint {
            font-family: monospace;
            background-color: #e9ecef;
            padding: 5px 10px;
            border-radius: 3px;
            display: inline-block;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 WordLoom Backend API Test</h1>
        
        <div class="status info">
            <strong>Project Status:</strong> Backend is running successfully! ✅
        </div>

        <div class="status success">
            <strong>✅ Backend Server:</strong> Running on http://localhost:5000<br>
            <strong>✅ Database:</strong> Connected to MongoDB<br>
            <strong>⚠️ Frontend:</strong> Node.js version compatibility issue (requires Node.js 20+)
        </div>

        <div class="test-section">
            <h3>🔧 Available API Endpoints</h3>
            <p>The backend provides the following REST API endpoints:</p>
            
            <h4>Authentication Routes:</h4>
            <div class="endpoint">POST /api/auth/register</div> - User registration<br>
            <div class="endpoint">POST /api/auth/login</div> - User login<br>
            <div class="endpoint">POST /api/auth/refresh</div> - Refresh token<br>
            <div class="endpoint">POST /api/auth/logout</div> - User logout<br>
            <div class="endpoint">POST /api/auth/logout-all</div> - Logout from all devices<br>

            <h4>Blog Routes:</h4>
            <div class="endpoint">GET /api/blogs</div> - Get all blogs<br>
            <div class="endpoint">POST /api/blogs</div> - Create new blog<br>
            <div class="endpoint">GET /api/blogs/:id</div> - Get specific blog<br>
            <div class="endpoint">PUT /api/blogs/:id</div> - Update blog<br>
            <div class="endpoint">DELETE /api/blogs/:id</div> - Delete blog<br>

            <h4>File Management Routes:</h4>
            <div class="endpoint">GET /api/files</div> - Get user files<br>
            <div class="endpoint">POST /api/files</div> - Create new file<br>
            <div class="endpoint">PUT /api/files/:id</div> - Update file<br>
            <div class="endpoint">DELETE /api/files/:id</div> - Delete file<br>
        </div>

        <div class="test-section">
            <h3>🧪 API Connection Test</h3>
            <button onclick="testConnection()">Test Backend Connection</button>
            <div id="connectionResult"></div>
        </div>

        <div class="test-section">
            <h3>📝 About WordLoom</h3>
            <p>WordLoom is a full-stack markdown editor and blog platform with the following features:</p>
            <ul>
                <li><strong>Markdown Editor:</strong> Rich text editing with live preview</li>
                <li><strong>File Management:</strong> Organize your documents and blogs</li>
                <li><strong>User Authentication:</strong> Secure login and registration</li>
                <li><strong>Blog Publishing:</strong> Share your content with the world</li>
                <li><strong>Export Options:</strong> Export to PDF and other formats</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🔧 Technical Details</h3>
            <p><strong>Backend Technology Stack:</strong></p>
            <ul>
                <li>Node.js with Express.js</li>
                <li>MongoDB with Mongoose</li>
                <li>JWT Authentication</li>
                <li>CORS enabled</li>
                <li>RESTful API design</li>
            </ul>
            
            <p><strong>Frontend Technology Stack:</strong></p>
            <ul>
                <li>React 18+ with TypeScript</li>
                <li>Vite build tool</li>
                <li>Tailwind CSS</li>
                <li>React Router</li>
                <li>Axios for API calls</li>
            </ul>
        </div>

        <div class="status info">
            <strong>Next Steps:</strong> To run the frontend, please upgrade to Node.js 20+ or use a compatible version of the dependencies.
        </div>
    </div>

    <script>
        async function testConnection() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.innerHTML = '<div class="status info">Testing connection...</div>';
            
            try {
                const response = await fetch('http://localhost:5000/api/blogs', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <div class="status success">
                            ✅ Backend connection successful!<br>
                            Status: ${response.status}<br>
                            Response: API is working correctly
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="status error">
                            ⚠️ Backend responded with status: ${response.status}<br>
                            This might be expected for protected routes.
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="status error">
                        ❌ Connection failed: ${error.message}<br>
                        Make sure the backend server is running on http://localhost:5000
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
