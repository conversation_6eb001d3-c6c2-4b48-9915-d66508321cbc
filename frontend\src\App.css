/* Reset and base styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

/* Modern scrollbar styles */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f3f4f6;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
  transition: background 0.2s ease;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Custom scrollbar for code editor */
.scrollbar-thumb-gray-600 {
  scrollbar-color: #4b5563 #374151;
}

.scrollbar-thumb-gray-600::-webkit-scrollbar-thumb {
  background: #4b5563;
}

.scrollbar-thumb-gray-600::-webkit-scrollbar-track {
  background: #374151;
}

.scrollbar-thumb-gray-300 {
  scrollbar-color: #d1d5db #f3f4f6;
}

.scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
  background: #d1d5db;
}

.scrollbar-thumb-gray-300::-webkit-scrollbar-track {
  background: #f3f4f6;
}

.scrollbar-track-gray-100 {
  scrollbar-color: #d1d5db #f3f4f6;
}

.scrollbar-track-gray-800 {
  scrollbar-color: #4b5563 #374151;
}

/* Hide scrollbar but keep functionality */
.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Smooth transitions */
.transition-width {
  transition: width 0.3s ease;
}

/* Focus visible for accessibility */
button:focus-visible,
input:focus-visible,
textarea:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Animation for floating elements */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Better focus styles for editor */
textarea:focus {
  outline: none;
  ring: 2px solid #3b82f6;
  ring-offset: 2px;
}

/* Prevent text selection on buttons */
button {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}