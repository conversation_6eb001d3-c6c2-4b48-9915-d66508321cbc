{"name": "react-md-editor", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"axios": "^1.6.0", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "jspdf": "^2.5.1", "lucide-react": "^0.263.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.11.0", "react-markdown": "^8.0.7", "react-md-editor-monorepo": "file:..", "react-router-dom": "^6.8.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.24", "tailwindcss": "^3.3.0", "typescript": "^5.0.2", "vite": "^4.4.5"}}